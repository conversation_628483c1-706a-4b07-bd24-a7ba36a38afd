#!/usr/bin/env python3
"""
🎯 UNIFIED ADMIN INTERFACE - HVAC CRM SYSTEM
Centralny interfejs administracyjny do zarządzania wszystkimi komponentami systemu HVAC CRM

Funkcjonalności:
- Monitoring Celery workers i tasków
- Zarządzanie agentami AI (Bielik V3, Gemma3-4b)
- Status wszystkich serwisów (GoBackend-Kratos, Gobeklitepe, Redis, MongoDB)
- Interfejs do transkrypcji i analizy emaili
- Dashboard z metrykami systemu
- Kontrola procesów i restartowanie serwisów

Author: HVAC CRM Team
Date: 2025-05-31
"""

import gradio as gr
import asyncio
import aiohttp
import redis
import psutil
import subprocess
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd
from pathlib import Path

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedAdminInterface:
    """Główna klasa interfejsu administracyjnego"""
    
    def __init__(self):
        self.redis_client = None
        self.services_status = {}
        self.celery_status = {}
        self.agents_status = {}
        
        # Konfiguracja serwisów
        self.services_config = {
            "GoBackend-Kratos": {"url": "http://localhost:8000/health", "port": 8000},
            "Gobeklitepe": {"url": "http://localhost:8082/v1/.well-known/ready", "port": 8082},
            "Redis": {"url": "**************:3037", "port": 3037},
            "MongoDB": {"url": "**************:27017", "port": 27017},
            "LM Studio": {"url": "http://*************:1234/v1/models", "port": 1234},
            "MinIO": {"url": "http://**************:9000", "port": 9000}
        }
        
        # Konfiguracja agentów AI
        self.agents_config = {
            "Bielik V3": {"url": "http://localhost:8877/v1/models", "port": 8877},
            "Gemma3-4b": {"url": "http://localhost:8878/v1/models", "port": 8878},
            "Gemma-3-4b-it": {"url": "http://localhost:8879/v1/models", "port": 8879}
        }
        
        self.init_connections()
    
    def init_connections(self):
        """Inicjalizacja połączeń z serwisami"""
        try:
            self.redis_client = redis.Redis(host='**************', port=3037, decode_responses=True)
            self.redis_client.ping()
            logger.info("✅ Połączono z Redis")
        except Exception as e:
            logger.error(f"❌ Błąd połączenia z Redis: {e}")
    
    async def check_service_status(self, service_name: str, config: Dict) -> Dict:
        """Sprawdza status pojedynczego serwisu"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(config["url"]) as response:
                    if response.status == 200 or response.status == 204:
                        return {
                            "name": service_name,
                            "status": "🟢 Online",
                            "response_time": "< 1s",
                            "last_check": datetime.now().strftime("%H:%M:%S")
                        }
        except Exception as e:
            return {
                "name": service_name,
                "status": "🔴 Offline",
                "response_time": "Timeout",
                "last_check": datetime.now().strftime("%H:%M:%S"),
                "error": str(e)
            }
    
    async def get_all_services_status(self) -> pd.DataFrame:
        """Pobiera status wszystkich serwisów"""
        tasks = []
        
        # Sprawdź serwisy
        for service_name, config in self.services_config.items():
            tasks.append(self.check_service_status(service_name, config))
        
        # Sprawdź agentów AI
        for agent_name, config in self.agents_config.items():
            tasks.append(self.check_service_status(agent_name, config))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Konwertuj na DataFrame
        valid_results = [r for r in results if isinstance(r, dict)]
        if valid_results:
            return pd.DataFrame(valid_results)
        else:
            return pd.DataFrame(columns=["name", "status", "response_time", "last_check"])
    
    def get_system_metrics(self) -> Dict:
        """Pobiera metryki systemu"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "CPU Usage": f"{cpu_percent}%",
                "Memory Usage": f"{memory.percent}%",
                "Memory Available": f"{memory.available / (1024**3):.1f} GB",
                "Disk Usage": f"{disk.percent}%",
                "Disk Free": f"{disk.free / (1024**3):.1f} GB",
                "Load Average": f"{psutil.getloadavg()[0]:.2f}" if hasattr(psutil, 'getloadavg') else "N/A"
            }
        except Exception as e:
            logger.error(f"Błąd pobierania metryk: {e}")
            return {"Error": str(e)}
    
    def get_celery_status(self) -> Dict:
        """Pobiera status Celery workers"""
        try:
            # Sprawdź aktywne procesy Celery
            celery_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'celery' in proc.info['name'].lower() or any('celery' in arg for arg in proc.info['cmdline']):
                        celery_processes.append({
                            'PID': proc.info['pid'],
                            'Name': proc.info['name'],
                            'Status': proc.status()
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                "Active Workers": len(celery_processes),
                "Processes": celery_processes,
                "Queue Status": "Checking..." if self.redis_client else "Redis Disconnected"
            }
        except Exception as e:
            return {"Error": str(e)}
    
    def restart_service(self, service_name: str) -> str:
        """Restartuje wybrany serwis"""
        try:
            if service_name == "Gobeklitepe":
                result = subprocess.run([
                    "docker-compose", "-f", "/home/<USER>/HVAC/unifikacja/Gobeklitepe/docker-compose.weaviate.yml", 
                    "restart"
                ], capture_output=True, text=True, cwd="/home/<USER>/HVAC/unifikacja/Gobeklitepe")
                
                if result.returncode == 0:
                    return f"✅ {service_name} został zrestartowany pomyślnie"
                else:
                    return f"❌ Błąd restartowania {service_name}: {result.stderr}"
            
            elif service_name == "GoBackend-Kratos":
                # Restart GoBackend-Kratos
                result = subprocess.run([
                    "docker-compose", "restart", "gobackend"
                ], capture_output=True, text=True, cwd="/home/<USER>/HVAC/unifikacja/GoBackend-Kratos-fresh")
                
                if result.returncode == 0:
                    return f"✅ {service_name} został zrestartowany pomyślnie"
                else:
                    return f"❌ Błąd restartowania {service_name}: {result.stderr}"
            
            else:
                return f"⚠️ Restart {service_name} nie jest obsługiwany przez interfejs"
                
        except Exception as e:
            return f"❌ Błąd restartowania {service_name}: {str(e)}"

def create_admin_interface():
    """Tworzy interfejs Gradio"""
    admin = UnifiedAdminInterface()
    
    def refresh_services():
        """Odświeża status serwisów"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            df = loop.run_until_complete(admin.get_all_services_status())
            loop.close()
            return df
        except Exception as e:
            logger.error(f"Błąd odświeżania serwisów: {e}")
            return pd.DataFrame({"Error": [str(e)]})
    
    def refresh_metrics():
        """Odświeża metryki systemu"""
        metrics = admin.get_system_metrics()
        return "\n".join([f"**{k}:** {v}" for k, v in metrics.items()])
    
    def refresh_celery():
        """Odświeża status Celery"""
        celery_status = admin.get_celery_status()
        return json.dumps(celery_status, indent=2)
    
    # Tworzenie interfejsu Gradio
    with gr.Blocks(title="🎯 HVAC CRM - Unified Admin Interface", theme=gr.themes.Soft()) as interface:
        gr.Markdown("# 🎯 HVAC CRM - Unified Admin Interface")
        gr.Markdown("Centralny panel administracyjny do zarządzania wszystkimi komponentami systemu")
        
        with gr.Tabs():
            # Tab 1: Services Status
            with gr.Tab("🔧 Services Status"):
                gr.Markdown("## Status wszystkich serwisów systemu")
                
                services_df = gr.Dataframe(
                    value=refresh_services(),
                    headers=["Service", "Status", "Response Time", "Last Check"],
                    interactive=False
                )
                
                with gr.Row():
                    refresh_services_btn = gr.Button("🔄 Refresh Services", variant="primary")
                    restart_service_dropdown = gr.Dropdown(
                        choices=list(admin.services_config.keys()) + list(admin.agents_config.keys()),
                        label="Select Service to Restart"
                    )
                    restart_btn = gr.Button("🔄 Restart Service", variant="secondary")
                
                restart_output = gr.Textbox(label="Restart Output", interactive=False)
                
                refresh_services_btn.click(refresh_services, outputs=services_df)
                restart_btn.click(admin.restart_service, inputs=restart_service_dropdown, outputs=restart_output)
            
            # Tab 2: System Metrics
            with gr.Tab("📊 System Metrics"):
                gr.Markdown("## Metryki systemu")
                
                metrics_text = gr.Markdown(value=refresh_metrics())
                refresh_metrics_btn = gr.Button("🔄 Refresh Metrics", variant="primary")
                
                refresh_metrics_btn.click(refresh_metrics, outputs=metrics_text)
            
            # Tab 3: Celery Management
            with gr.Tab("⚙️ Celery Management"):
                gr.Markdown("## Zarządzanie Celery Workers")
                
                celery_status_text = gr.Code(value=refresh_celery(), language="json", label="Celery Status")
                refresh_celery_btn = gr.Button("🔄 Refresh Celery", variant="primary")
                
                refresh_celery_btn.click(refresh_celery, outputs=celery_status_text)
            
            # Tab 4: Quick Actions
            with gr.Tab("⚡ Quick Actions"):
                gr.Markdown("## Szybkie akcje")
                
                with gr.Row():
                    gr.Button("🚀 Start All Services", variant="primary")
                    gr.Button("⏹️ Stop All Services", variant="secondary")
                    gr.Button("🔄 Restart All Services", variant="secondary")
                
                gr.Markdown("### Logs Viewer")
                log_service = gr.Dropdown(
                    choices=["GoBackend-Kratos", "Gobeklitepe", "Python Mixer"],
                    label="Select Service for Logs"
                )
                logs_output = gr.Code(language="shell", label="Service Logs")
    
    return interface

if __name__ == "__main__":
    interface = create_admin_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        debug=True
    )
