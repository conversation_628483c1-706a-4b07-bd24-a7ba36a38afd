package models

import (
	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"
)

// GetGenerators returns all table generators for HVAC CRM Admin Dashboard
func GetGenerators() map[string]table.Generator {
	return map[string]table.Generator{
		"customers":        GetCustomersTable,
		"service_orders":   GetServiceOrdersTable,
		"technicians":      GetTechniciansTable,
		"equipment":        GetEquipmentTable,
		"invoices":         GetInvoicesTable,
		"email_analysis":   GetEmailAnalysisTable,
		"transcriptions":   GetTranscriptionsTable,
		"system_logs":      GetSystemLogsTable,
		"user_management":  GetUserManagementTable,
		"system_metrics":   GetSystemMetricsTable,
	}
}

// GetCustomersTable generates the customers management table
func GetCustomersTable(ctx *context.Context) table.Table {
	customersTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := customersTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Name", "name", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Email", "email", db.Varchar).
		FieldFilterable()
	info.AddField("Phone", "phone", db.Varchar).
		FieldFilterable()
	info.AddField("Address", "address", db.Text)
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "active":
				return "<span class='label label-success'>Active</span>"
			case "inactive":
				return "<span class='label label-warning'>Inactive</span>"
			case "vip":
				return "<span class='label label-primary'>VIP</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		})
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()
	info.AddField("Updated At", "updated_at", db.Timestamp).
		FieldSortable()

	info.SetTable("customers").SetTitle("Customers").SetDescription("HVAC Customer Management")

	formList := customersTable.GetForm()
	formList.AddField("ID", "id", db.Int, form.Default).FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()
	formList.AddField("Name", "name", db.Varchar, form.Text).FieldMust()
	formList.AddField("Email", "email", db.Varchar, form.Email).FieldMust()
	formList.AddField("Phone", "phone", db.Varchar, form.Text)
	formList.AddField("Address", "address", db.Text, form.TextArea)
	formList.AddField("Status", "status", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Active", Value: "active"},
			{Text: "Inactive", Value: "inactive"},
			{Text: "VIP", Value: "vip"},
		}).FieldDefault("active")
	formList.AddField("Created At", "created_at", db.Timestamp, form.Datetime).
		FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()
	formList.AddField("Updated At", "updated_at", db.Timestamp, form.Datetime).
		FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()

	formList.SetTable("customers").SetTitle("Customers").SetDescription("HVAC Customer Management")

	return customersTable
}

// GetServiceOrdersTable generates the service orders management table
func GetServiceOrdersTable(ctx *context.Context) table.Table {
	serviceOrdersTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := serviceOrdersTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Customer", "customer_name", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Service Type", "service_type", db.Varchar).
		FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "pending":
				return "<span class='label label-warning'>Pending</span>"
			case "in_progress":
				return "<span class='label label-info'>In Progress</span>"
			case "completed":
				return "<span class='label label-success'>Completed</span>"
			case "cancelled":
				return "<span class='label label-danger'>Cancelled</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Priority", "priority", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "low":
				return "<span class='label label-default'>Low</span>"
			case "normal":
				return "<span class='label label-primary'>Normal</span>"
			case "high":
				return "<span class='label label-warning'>High</span>"
			case "urgent":
				return "<span class='label label-danger'>Urgent</span>"
			default:
				return "<span class='label label-default'>Normal</span>"
			}
		}).FieldFilterable()
	info.AddField("Scheduled Date", "scheduled_date", db.Timestamp).
		FieldSortable()
	info.AddField("Total Amount", "total_amount", db.Decimal).
		FieldDisplay(func(value types.FieldModel) interface{} {
			return value.Value + " PLN"
		}).FieldSortable()
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()

	info.SetTable("service_orders").SetTitle("Service Orders").SetDescription("HVAC Service Order Management")

	formList := serviceOrdersTable.GetForm()
	formList.AddField("ID", "id", db.Int, form.Default).FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()
	formList.AddField("Customer ID", "customer_id", db.Int, form.SelectSingle).FieldMust()
	formList.AddField("Service Type", "service_type", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Installation", Value: "installation"},
			{Text: "Maintenance", Value: "maintenance"},
			{Text: "Repair", Value: "repair"},
			{Text: "Inspection", Value: "inspection"},
		}).FieldMust()
	formList.AddField("Description", "description", db.Text, form.TextArea)
	formList.AddField("Status", "status", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Pending", Value: "pending"},
			{Text: "In Progress", Value: "in_progress"},
			{Text: "Completed", Value: "completed"},
			{Text: "Cancelled", Value: "cancelled"},
		}).FieldDefault("pending")
	formList.AddField("Priority", "priority", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Low", Value: "low"},
			{Text: "Normal", Value: "normal"},
			{Text: "High", Value: "high"},
			{Text: "Urgent", Value: "urgent"},
		}).FieldDefault("normal")
	formList.AddField("Scheduled Date", "scheduled_date", db.Timestamp, form.Datetime)
	formList.AddField("Total Amount", "total_amount", db.Decimal, form.Currency)
	formList.AddField("Created At", "created_at", db.Timestamp, form.Datetime).
		FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()

	formList.SetTable("service_orders").SetTitle("Service Orders").SetDescription("HVAC Service Order Management")

	return serviceOrdersTable
}

// GetTechniciansTable generates the technicians management table
func GetTechniciansTable(ctx *context.Context) table.Table {
	techniciansTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := techniciansTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Name", "name", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Email", "email", db.Varchar).
		FieldFilterable()
	info.AddField("Phone", "phone", db.Varchar).
		FieldFilterable()
	info.AddField("Specialization", "specialization", db.Varchar).
		FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "available":
				return "<span class='label label-success'>Available</span>"
			case "busy":
				return "<span class='label label-warning'>Busy</span>"
			case "off_duty":
				return "<span class='label label-default'>Off Duty</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Rating", "rating", db.Decimal).
		FieldDisplay(func(value types.FieldModel) interface{} {
			return "⭐ " + value.Value + "/5"
		}).FieldSortable()
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()

	info.SetTable("technicians").SetTitle("Technicians").SetDescription("HVAC Technician Management")

	formList := techniciansTable.GetForm()
	formList.AddField("ID", "id", db.Int, form.Default).FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()
	formList.AddField("Name", "name", db.Varchar, form.Text).FieldMust()
	formList.AddField("Email", "email", db.Varchar, form.Email).FieldMust()
	formList.AddField("Phone", "phone", db.Varchar, form.Text)
	formList.AddField("Specialization", "specialization", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Air Conditioning", Value: "air_conditioning"},
			{Text: "Heating", Value: "heating"},
			{Text: "Ventilation", Value: "ventilation"},
			{Text: "Heat Pumps", Value: "heat_pumps"},
			{Text: "General HVAC", Value: "general"},
		})
	formList.AddField("Status", "status", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Available", Value: "available"},
			{Text: "Busy", Value: "busy"},
			{Text: "Off Duty", Value: "off_duty"},
		}).FieldDefault("available")
	formList.AddField("Rating", "rating", db.Decimal, form.Number)
	formList.AddField("Created At", "created_at", db.Timestamp, form.Datetime).
		FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()

	formList.SetTable("technicians").SetTitle("Technicians").SetDescription("HVAC Technician Management")

	return techniciansTable
}

// GetEquipmentTable generates the equipment management table
func GetEquipmentTable(ctx *context.Context) table.Table {
	equipmentTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := equipmentTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Customer", "customer_name", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Type", "equipment_type", db.Varchar).
		FieldFilterable()
	info.AddField("Brand", "brand", db.Varchar).
		FieldFilterable()
	info.AddField("Model", "model", db.Varchar).
		FieldFilterable()
	info.AddField("Serial Number", "serial_number", db.Varchar).
		FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "operational":
				return "<span class='label label-success'>Operational</span>"
			case "maintenance":
				return "<span class='label label-warning'>Maintenance</span>"
			case "repair":
				return "<span class='label label-danger'>Repair</span>"
			case "retired":
				return "<span class='label label-default'>Retired</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Installation Date", "installation_date", db.Date).
		FieldSortable()
	info.AddField("Last Service", "last_service_date", db.Date).
		FieldSortable()

	info.SetTable("equipment").SetTitle("Equipment").SetDescription("HVAC Equipment Management")

	formList := equipmentTable.GetForm()
	formList.AddField("ID", "id", db.Int, form.Default).FieldDisplayButCanNotEditWhenUpdate().FieldDisableWhenCreate()
	formList.AddField("Customer ID", "customer_id", db.Int, form.SelectSingle).FieldMust()
	formList.AddField("Equipment Type", "equipment_type", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Air Conditioner", Value: "air_conditioner"},
			{Text: "Heat Pump", Value: "heat_pump"},
			{Text: "Furnace", Value: "furnace"},
			{Text: "Ventilation System", Value: "ventilation"},
			{Text: "Thermostat", Value: "thermostat"},
		}).FieldMust()
	formList.AddField("Brand", "brand", db.Varchar, form.Text).FieldMust()
	formList.AddField("Model", "model", db.Varchar, form.Text).FieldMust()
	formList.AddField("Serial Number", "serial_number", db.Varchar, form.Text).FieldMust()
	formList.AddField("Status", "status", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "Operational", Value: "operational"},
			{Text: "Maintenance", Value: "maintenance"},
			{Text: "Repair", Value: "repair"},
			{Text: "Retired", Value: "retired"},
		}).FieldDefault("operational")
	formList.AddField("Installation Date", "installation_date", db.Date, form.Date)
	formList.AddField("Last Service Date", "last_service_date", db.Date, form.Date)
	formList.AddField("Warranty Expiry", "warranty_expiry", db.Date, form.Date)

	formList.SetTable("equipment").SetTitle("Equipment").SetDescription("HVAC Equipment Management")

	return equipmentTable
}

// GetEmailAnalysisTable generates the email analysis table
func GetEmailAnalysisTable(ctx *context.Context) table.Table {
	emailTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := emailTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("From", "from_email", db.Varchar).
		FieldFilterable()
	info.AddField("Subject", "subject", db.Varchar).
		FieldFilterable()
	info.AddField("Sentiment", "sentiment", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "positive":
				return "<span class='label label-success'>😊 Positive</span>"
			case "neutral":
				return "<span class='label label-info'>😐 Neutral</span>"
			case "negative":
				return "<span class='label label-danger'>😞 Negative</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Priority", "priority", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "high":
				return "<span class='label label-danger'>🔥 High</span>"
			case "medium":
				return "<span class='label label-warning'>⚡ Medium</span>"
			case "low":
				return "<span class='label label-success'>📝 Low</span>"
			default:
				return "<span class='label label-default'>Normal</span>"
			}
		}).FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "processed":
				return "<span class='label label-success'>✅ Processed</span>"
			case "pending":
				return "<span class='label label-warning'>⏳ Pending</span>"
			case "failed":
				return "<span class='label label-danger'>❌ Failed</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Received At", "received_at", db.Timestamp).
		FieldSortable()
	info.AddField("Processed At", "processed_at", db.Timestamp).
		FieldSortable()

	info.SetTable("email_analysis").SetTitle("Email Intelligence").SetDescription("AI-Powered Email Analysis")

	return emailTable
}

// GetTranscriptionsTable generates the transcriptions table
func GetTranscriptionsTable(ctx *context.Context) table.Table {
	transcriptionsTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := transcriptionsTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("File Name", "file_name", db.Varchar).
		FieldFilterable()
	info.AddField("Duration", "duration", db.Int).
		FieldDisplay(func(value types.FieldModel) interface{} {
			return value.Value + "s"
		}).FieldSortable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "completed":
				return "<span class='label label-success'>✅ Completed</span>"
			case "processing":
				return "<span class='label label-info'>🔄 Processing</span>"
			case "queued":
				return "<span class='label label-warning'>⏳ Queued</span>"
			case "failed":
				return "<span class='label label-danger'>❌ Failed</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Accuracy", "accuracy", db.Decimal).
		FieldDisplay(func(value types.FieldModel) interface{} {
			return value.Value + "%"
		}).FieldSortable()
	info.AddField("Language", "language", db.Varchar).
		FieldFilterable()
	info.AddField("Model Used", "model_used", db.Varchar).
		FieldFilterable()
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()
	info.AddField("Completed At", "completed_at", db.Timestamp).
		FieldSortable()

	info.SetTable("transcriptions").SetTitle("Transcriptions").SetDescription("AI Speech-to-Text Processing")

	return transcriptionsTable
}

// GetSystemLogsTable generates the system logs table
func GetSystemLogsTable(ctx *context.Context) table.Table {
	logsTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := logsTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Level", "level", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "error":
				return "<span class='label label-danger'>🚨 ERROR</span>"
			case "warning":
				return "<span class='label label-warning'>⚠️ WARNING</span>"
			case "info":
				return "<span class='label label-info'>ℹ️ INFO</span>"
			case "debug":
				return "<span class='label label-default'>🐛 DEBUG</span>"
			default:
				return "<span class='label label-default'>LOG</span>"
			}
		}).FieldFilterable()
	info.AddField("Service", "service", db.Varchar).
		FieldFilterable()
	info.AddField("Message", "message", db.Text).
		FieldFilterable()
	info.AddField("User", "user_id", db.Varchar).
		FieldFilterable()
	info.AddField("IP Address", "ip_address", db.Varchar).
		FieldFilterable()
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()

	info.SetTable("system_logs").SetTitle("System Logs").SetDescription("System Activity Monitoring")

	return logsTable
}

// GetUserManagementTable generates the user management table
func GetUserManagementTable(ctx *context.Context) table.Table {
	usersTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := usersTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Username", "username", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Email", "email", db.Varchar).
		FieldFilterable()
	info.AddField("Role", "role", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "admin":
				return "<span class='label label-danger'>👑 Admin</span>"
			case "manager":
				return "<span class='label label-warning'>👨‍💼 Manager</span>"
			case "technician":
				return "<span class='label label-info'>🔧 Technician</span>"
			case "user":
				return "<span class='label label-success'>👤 User</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "active":
				return "<span class='label label-success'>✅ Active</span>"
			case "inactive":
				return "<span class='label label-warning'>⏸️ Inactive</span>"
			case "suspended":
				return "<span class='label label-danger'>🚫 Suspended</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Last Login", "last_login", db.Timestamp).
		FieldSortable()
	info.AddField("Created At", "created_at", db.Timestamp).
		FieldSortable()

	info.SetTable("users").SetTitle("User Management").SetDescription("System User Administration")

	return usersTable
}

// GetSystemMetricsTable generates the system metrics table
func GetSystemMetricsTable(ctx *context.Context) table.Table {
	metricsTable := table.NewDefaultTable(table.DefaultConfigWithDriver("postgres"))

	info := metricsTable.GetInfo().HideFilterArea()

	info.AddField("ID", "id", db.Int).
		FieldFilterable().FieldSortable()
	info.AddField("Metric Name", "metric_name", db.Varchar).
		FieldFilterable().FieldSortable()
	info.AddField("Value", "value", db.Decimal).
		FieldSortable()
	info.AddField("Unit", "unit", db.Varchar)
	info.AddField("Service", "service", db.Varchar).
		FieldFilterable()
	info.AddField("Status", "status", db.Varchar).
		FieldDisplay(func(value types.FieldModel) interface{} {
			switch value.Value {
			case "healthy":
				return "<span class='label label-success'>💚 Healthy</span>"
			case "warning":
				return "<span class='label label-warning'>⚠️ Warning</span>"
			case "critical":
				return "<span class='label label-danger'>🚨 Critical</span>"
			default:
				return "<span class='label label-default'>Unknown</span>"
			}
		}).FieldFilterable()
	info.AddField("Recorded At", "recorded_at", db.Timestamp).
		FieldSortable()

	info.SetTable("system_metrics").SetTitle("System Metrics").SetDescription("Performance & Health Monitoring")

	return metricsTable
}
