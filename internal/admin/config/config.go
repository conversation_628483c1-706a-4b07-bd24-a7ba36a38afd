package config

import (
	"github.com/GoAdminGroup/go-admin/modules/config"
	"github.com/GoAdminGroup/go-admin/modules/language"
)

// HVACAdminConfig represents the configuration for HVAC Admin Dashboard
type HVACAdminConfig struct {
	// Database settings
	Database DatabaseConfig `yaml:"database"`
	
	// Admin panel settings
	Admin AdminConfig `yaml:"admin"`
	
	// HVAC-specific settings
	HVAC HVACConfig `yaml:"hvac"`
	
	// Integration settings
	Integrations IntegrationConfig `yaml:"integrations"`
	
	// Security settings
	Security SecurityConfig `yaml:"security"`
}

type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Name     string `yaml:"name"`
	Driver   string `yaml:"driver"`
	MaxIdle  int    `yaml:"max_idle"`
	MaxOpen  int    `yaml:"max_open"`
}

type AdminConfig struct {
	URLPrefix       string `yaml:"url_prefix"`
	Theme          string `yaml:"theme"`
	Language       string `yaml:"language"`
	Debug          bool   `yaml:"debug"`
	SessionTimeout int    `yaml:"session_timeout"`
	UploadPath     string `yaml:"upload_path"`
}

type HVACConfig struct {
	CompanyName    string   `yaml:"company_name"`
	SupportEmail   string   `yaml:"support_email"`
	ServiceZones   []string `yaml:"service_zones"`
	DefaultCurrency string  `yaml:"default_currency"`
	WorkingHours   WorkingHours `yaml:"working_hours"`
}

type WorkingHours struct {
	Start string `yaml:"start"`
	End   string `yaml:"end"`
	Days  []string `yaml:"days"`
}

type IntegrationConfig struct {
	GoBackendKratos GoBackendConfig `yaml:"gobackend_kratos"`
	Redis          RedisConfig     `yaml:"redis"`
	MongoDB        MongoConfig     `yaml:"mongodb"`
	MinIO          MinIOConfig     `yaml:"minio"`
	AI             AIConfig        `yaml:"ai"`
}

type GoBackendConfig struct {
	BaseURL string `yaml:"base_url"`
	APIKey  string `yaml:"api_key"`
	Timeout int    `yaml:"timeout"`
}

type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

type MongoConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

type MinIOConfig struct {
	Endpoint  string `yaml:"endpoint"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	Bucket    string `yaml:"bucket"`
}

type AIConfig struct {
	LMStudio LMStudioConfig `yaml:"lm_studio"`
	Models   []ModelConfig  `yaml:"models"`
}

type LMStudioConfig struct {
	BaseURL string `yaml:"base_url"`
	APIKey  string `yaml:"api_key"`
}

type ModelConfig struct {
	Name     string `yaml:"name"`
	Endpoint string `yaml:"endpoint"`
	Type     string `yaml:"type"`
}

type SecurityConfig struct {
	JWTSecret      string   `yaml:"jwt_secret"`
	AllowedOrigins []string `yaml:"allowed_origins"`
	RateLimit      int      `yaml:"rate_limit"`
	EnableHTTPS    bool     `yaml:"enable_https"`
}

// GetDefaultConfig returns default configuration for HVAC Admin Dashboard
func GetDefaultConfig() *HVACAdminConfig {
	return &HVACAdminConfig{
		Database: DatabaseConfig{
			Host:     "**************",
			Port:     "5432",
			User:     "hvacdb",
			Password: "blaeritipol",
			Name:     "hvacdb",
			Driver:   "postgres",
			MaxIdle:  50,
			MaxOpen:  150,
		},
		Admin: AdminConfig{
			URLPrefix:       "admin",
			Theme:          "adminlte",
			Language:       "en",
			Debug:          true,
			SessionTimeout: 7200,
			UploadPath:     "./uploads",
		},
		HVAC: HVACConfig{
			CompanyName:     "Fulmark Klimatyzacja",
			SupportEmail:    "<EMAIL>",
			ServiceZones:    []string{"Warszawa", "Kraków", "Gdańsk"},
			DefaultCurrency: "PLN",
			WorkingHours: WorkingHours{
				Start: "08:00",
				End:   "18:00",
				Days:  []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday"},
			},
		},
		Integrations: IntegrationConfig{
			GoBackendKratos: GoBackendConfig{
				BaseURL: "http://localhost:8080",
				APIKey:  "",
				Timeout: 30,
			},
			Redis: RedisConfig{
				Host:     "**************",
				Port:     "3037",
				Password: "",
				DB:       0,
			},
			MongoDB: MongoConfig{
				Host:     "**************",
				Port:     "27017",
				Database: "hvac_logs",
				Username: "",
				Password: "",
			},
			MinIO: MinIOConfig{
				Endpoint:  "**************:9000",
				AccessKey: "",
				SecretKey: "",
				Bucket:    "hvac-files",
			},
			AI: AIConfig{
				LMStudio: LMStudioConfig{
					BaseURL: "http://*************:1234",
					APIKey:  "",
				},
				Models: []ModelConfig{
					{
						Name:     "Bielik V3",
						Endpoint: "http://localhost:8877",
						Type:     "polish_language",
					},
					{
						Name:     "Gemma3-4b",
						Endpoint: "http://localhost:8879",
						Type:     "general",
					},
				},
			},
		},
		Security: SecurityConfig{
			JWTSecret:      "hvac-admin-secret-key",
			AllowedOrigins: []string{"*"},
			RateLimit:      100,
			EnableHTTPS:    false,
		},
	}
}

// ToGoAdminConfig converts HVACAdminConfig to go-admin Config
func (c *HVACAdminConfig) ToGoAdminConfig() config.Config {
	return config.Config{
		Databases: config.DatabaseList{
			"default": {
				Host:       c.Database.Host,
				Port:       c.Database.Port,
				User:       c.Database.User,
				Pwd:        c.Database.Password,
				Name:       c.Database.Name,
				MaxIdleCon: c.Database.MaxIdle,
				MaxOpenCon: c.Database.MaxOpen,
				Driver:     c.Database.Driver,
			},
		},
		UrlPrefix: c.Admin.URLPrefix,
		Store: config.Store{
			Path:   c.Admin.UploadPath,
			Prefix: "uploads",
		},
		Language:        getLanguage(c.Admin.Language),
		Debug:           c.Admin.Debug,
		ColorScheme:     getColorScheme(c.Admin.Theme),
		SessionLifeTime: c.Admin.SessionTimeout,
		InfoLogPath:     "./logs/admin-info.log",
		AccessLogPath:   "./logs/admin-access.log",
		ErrorLogPath:    "./logs/admin-error.log",
		LoginUrl:        "/admin/login",
		NoLimitLoginIP:  false,
		Custom: map[string]interface{}{
			"app_name":        "HVAC CRM Admin",
			"app_description": "Comprehensive HVAC business management system",
			"company_name":    c.HVAC.CompanyName,
			"support_email":   c.HVAC.SupportEmail,
		},
	}
}

func getLanguage(lang string) language.Language {
	switch lang {
	case "pl":
		return language.PL
	case "en":
		return language.EN
	default:
		return language.EN
	}
}

func getColorScheme(theme string) string {
	switch theme {
	case "blue":
		return "skin-blue"
	case "green":
		return "skin-green"
	case "red":
		return "skin-red"
	case "yellow":
		return "skin-yellow"
	default:
		return "skin-blue"
	}
}
