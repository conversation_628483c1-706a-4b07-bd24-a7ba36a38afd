package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/GoAdminGroup/go-admin/engine"
	"github.com/GoAdminGroup/go-admin/modules/config"
	"github.com/GoAdminGroup/go-admin/modules/language"
	"github.com/GoAdminGroup/go-admin/template"
	"github.com/GoAdminGroup/go-admin/template/chartjs"
	"github.com/gin-gonic/gin"
	_ "github.com/GoAdminGroup/go-admin/adapter/gin"
	_ "github.com/GoAdminGroup/go-admin/modules/db/drivers/postgres"

	"gobackend-hvac-kratos/internal/admin/models"
	"gobackend-hvac-kratos/internal/admin/handlers"
)

// 🚀 HVAC CRM Admin Dashboard - Potężny system zarządzania całym ekosystemem
// Wykorzystuje go-admin framework dla profesjonalnego interfejsu administracyjnego

const (
	ServiceName    = "hvac-admin-dashboard"
	ServiceVersion = "1.0.0"
	DefaultPort    = "8090"
)

func main() {
	log.Println("🚀 Starting HVAC CRM Admin Dashboard...")
	log.Println("🎯 Framework: go-admin")
	log.Println("🔧 Target: Complete HVAC ecosystem management")

	// Initialize Gin router
	r := gin.Default()

	// Initialize go-admin engine
	eng := engine.Default()

	// Load configuration
	cfg := loadAdminConfig()

	// Add chartjs component for visualizations
	template.AddComp(chartjs.NewChart())

	// Configure go-admin
	if err := eng.AddConfig(&cfg).
		AddGenerators(models.GetGenerators()).
		Use(r); err != nil {
		log.Fatal("Failed to initialize admin engine:", err)
	}	// Add custom routes for HVAC-specific functionality
	setupCustomRoutes(r, eng)

	// Setup graceful shutdown
	srv := &http.Server{
		Addr:    ":" + getPort(),
		Handler: r,
	}

	// Start server in goroutine
	go func() {
		log.Printf("🌐 HVAC Admin Dashboard starting on port %s", getPort())
		log.Printf("📊 Dashboard URL: http://localhost:%s/admin", getPort())
		log.Printf("🔗 API URL: http://localhost:%s/api", getPort())
		
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Server failed to start:", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("🛑 Shutting down HVAC Admin Dashboard...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("✅ HVAC Admin Dashboard stopped gracefully")
}

func loadAdminConfig() config.Config {
	return config.Config{
		// Database configuration - using existing PostgreSQL
		Databases: config.DatabaseList{
			"default": {
				Host:       "**************",
				Port:       "5432",
				User:       "hvacdb",
				Pwd:        "blaeritipol",
				Name:       "hvacdb",
				MaxIdleCon: 50,
				MaxOpenCon: 150,
				Driver:     "postgres",
			},
		},
		
		// Admin panel configuration
		UrlPrefix: "admin",
		
		// File storage configuration
		Store: config.Store{
			Path:   "./uploads",
			Prefix: "uploads",
		},		
		// Language configuration
		Language: language.EN,
		
		// Development mode
		Debug: true,
		
		// Logging configuration
		InfoLogPath:   "./logs/admin-info.log",
		AccessLogPath: "./logs/admin-access.log",
		ErrorLogPath:  "./logs/admin-error.log",
		
		// Theme configuration
		ColorScheme: "skin-blue",
		
		// Session configuration
		SessionLifeTime: 7200, // 2 hours
		
		// Security configuration
		LoginUrl:    "/admin/login",
		NoLimitLoginIP: false,
		
		// Custom configuration for HVAC CRM
		Custom: map[string]interface{}{
			"app_name":        "HVAC CRM Admin",
			"app_description": "Comprehensive HVAC business management system",
			"company_name":    "Fulmark Klimatyzacja",
			"support_email":   "<EMAIL>",
		},
	}
}

func setupCustomRoutes(r *gin.Engine, eng *engine.Engine) {
	// Initialize handlers
	healthHandler := handlers.NewHealthHandler()
	dashboardHandler := handlers.NewDashboardHandler()

	// API routes for HVAC-specific functionality
	api := r.Group("/api")
	{
		// System monitoring & health
		api.GET("/system/health", healthHandler.GetSystemHealth)
		api.GET("/containers/health", healthHandler.GetContainerHealth)
		api.GET("/gobeklitepe/status", healthHandler.GetGobeklitepeStatus)
		
		// Dashboard data
		api.GET("/dashboard/main", dashboardHandler.GetMainDashboard)
		api.GET("/dashboard/hvac", dashboardHandler.GetHVACMetrics)
		api.GET("/dashboard/ai", dashboardHandler.GetAIMetrics)
		api.GET("/dashboard/email", dashboardHandler.GetEmailMetrics)
		api.GET("/dashboard/activity", dashboardHandler.GetRecentActivity)
		api.GET("/dashboard/alerts", dashboardHandler.GetAlerts)
	}
	
	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": ServiceName,
			"version": ServiceVersion,
			"time":    time.Now().Format(time.RFC3339),
		})
	})
}func getPort() string {
	if port := os.Getenv("ADMIN_PORT"); port != "" {
		return port
	}
	return DefaultPort
}