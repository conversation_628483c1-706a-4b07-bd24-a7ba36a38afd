package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// DashboardHandler handles dashboard-related requests
type DashboardHandler struct {
	// Add dependencies here
}

// NewDashboardHandler creates a new dashboard handler
func NewDashboardHandler() *DashboardHandler {
	return &DashboardHandler{}
}

// GetMainDashboard returns the main dashboard data
func (h *DashboardHandler) GetMainDashboard(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "operational",
		"timestamp": time.Now().Format(time.RFC3339),
		"message": "HVAC Admin Dashboard - Main Dashboard",
	})
}

// GetHVACMetrics returns HVAC business metrics
func (h *DashboardHandler) GetHVACMetrics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"active_jobs": 25,
		"pending_jobs": 8,
		"completed_today": 12,
		"revenue_today": "8,450 PLN",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetAIMetrics returns AI services metrics
func (h *DashboardHandler) GetAIMetrics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"bielik_v3": "running",
		"gemma3": "running",
		"lm_studio": "connected",
		"models_loaded": 4,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetEmailMetrics returns email processing metrics
func (h *DashboardHandler) GetEmailMetrics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"processed_today": 127,
		"pending_analysis": 8,
		"sentiment_positive": "78%",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}// GetRecentActivity returns recent system activity
func (h *DashboardHandler) GetRecentActivity(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"activity": []map[string]interface{}{
			{
				"type": "job_completed",
				"description": "AC installation completed",
				"timestamp": time.Now().Add(-15 * time.Minute).Format(time.RFC3339),
			},
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetAlerts returns system alerts
func (h *DashboardHandler) GetAlerts(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"alerts": []map[string]interface{}{
			{
				"level": "warning",
				"message": "High CPU usage detected",
				"timestamp": time.Now().Add(-5 * time.Minute).Format(time.RFC3339),
			},
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}