package models

import (
	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
)

// GetGenerators returns all table generators for HVAC CRM Admin Dashboard
func GetGenerators() map[string]table.Generator {
	return map[string]table.Generator{
		"customers": GetCustomersTable,
	}
}

// Simplified table generator for compilation
func GetCustomersTable(ctx *context.Context) table.Table {
	cfg := table.DefaultConfigWithDriver("postgres")
	customersTable := table.NewDefaultTable(cfg)
	
	info := customersTable.GetInfo()
	info.SetTable("customers").SetTitle("Customers").SetDescription("HVAC Customer Management")
	
	return customersTable
}